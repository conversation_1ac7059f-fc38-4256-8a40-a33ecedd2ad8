/* Quiz Form Styles */
.quiz-form-section {
  min-height: calc(100vh - 120px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.quiz-form-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
}

.quiz-form-content {
  text-align: center;
}

.quiz-form-header {
  margin-bottom: 40px;
}

.quiz-form-avatar {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.quiz-form-avatar .quiz-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
}

.quiz-form-title {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.quiz-form-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 40px 0;
  line-height: 1.5;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.quiz-name-form {
  max-width: 400px;
  margin: 0 auto;
}

.quiz-form-fields {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.quiz-form-field {
  flex: 1;
}

.quiz-form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.quiz-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quiz-form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.quiz-form-button-wrapper {
  text-align: center;
}

.quiz-form-button {
  background-color: #6b7280;
  color: #ffffff;
  border: none;
  border-radius: 50px;
  padding: 16px 48px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.quiz-form-button:hover {
  background-color: #4b5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quiz-form-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quiz-form-button:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Tablet styles */
@media (max-width: 768px) {
  .quiz-form-section {
    padding: 40px 20px;
    min-height: calc(100vh - 100px);
  }
  
  .quiz-form-title {
    font-size: 28px;
  }
  
  .quiz-form-subtitle {
    font-size: 15px;
    margin-bottom: 32px;
  }
  
  .quiz-form-fields {
    gap: 12px;
    margin-bottom: 28px;
  }
  
  .quiz-form-input {
    padding: 14px 16px;
    font-size: 13px;
  }
  
  .quiz-form-button {
    padding: 14px 40px;
    font-size: 13px;
  }
}

/* Mobile styles */
@media (max-width: 480px) {
  .quiz-form-section {
    padding: 30px 15px;
  }
  
  .quiz-form-title {
    font-size: 24px;
    margin-bottom: 12px;
  }
  
  .quiz-form-subtitle {
    font-size: 14px;
    margin-bottom: 28px;
  }
  
  .quiz-form-fields {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .quiz-form-input {
    padding: 12px 16px;
    font-size: 12px;
  }
  
  .quiz-form-button {
    padding: 12px 32px;
    font-size: 12px;
    width: 100%;
    max-width: 200px;
  }
}

/* Animation for form transitions */
.quiz-form-section {
  opacity: 1;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.quiz-form-section.hiding {
  opacity: 0;
  transform: translateY(-20px);
}

/* Focus states for accessibility */
.quiz-form-input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.quiz-form-button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading state */
.quiz-form-button.loading {
  position: relative;
  color: transparent;
}

.quiz-form-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
