/* Quiz Header Styles */
.quiz-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 0;
  position: relative;
  z-index: 100;
}

.quiz-header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

/* Left side - Logo */
.quiz-header-left {
  flex: 0 0 auto;
  z-index: 10;
}

.quiz-header-logo {
  display: inline-block;
  text-decoration: none;
}

.quiz-logo-img {
  max-height: 40px;
  width: auto;
  display: block;
}

.quiz-logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
}

/* Center - Avatar and content */
.quiz-header-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  margin: 0 20px;
}

.quiz-header-avatar {
  margin-bottom: 20px;
}

.quiz-avatar-img,
.quiz-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.quiz-avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
}

.quiz-header-content {
  max-width: 500px;
}

.quiz-header-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.quiz-header-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* Right side - Back button */
.quiz-header-right {
  flex: 0 0 auto;
  z-index: 10;
}

.quiz-back-button {
  display: inline-block;
  padding: 10px 20px;
  background-color: transparent;
  color: #6b7280;
  text-decoration: none;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.quiz-back-button:hover {
  background-color: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
}

/* Mobile menu toggle - hidden by default */
.quiz-header-mobile-toggle {
  display: none;
  z-index: 20;
}

.quiz-mobile-menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.quiz-hamburger-line {
  width: 24px;
  height: 2px;
  background-color: #374151;
  margin: 2px 0;
  transition: 0.3s;
  border-radius: 1px;
}

.quiz-mobile-menu-btn.active .quiz-hamburger-line:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.quiz-mobile-menu-btn.active .quiz-hamburger-line:nth-child(2) {
  opacity: 0;
}

.quiz-mobile-menu-btn.active .quiz-hamburger-line:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile menu */
.quiz-mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 15;
}

.quiz-mobile-menu.active {
  display: block;
}

.quiz-mobile-menu-content {
  padding: 20px;
  text-align: center;
}

.quiz-mobile-back-button {
  display: inline-block;
  padding: 12px 24px;
  background-color: #f3f4f6;
  color: #374151;
  text-decoration: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
}

/* Tablet styles */
@media (max-width: 1024px) {
  .quiz-header-container {
    padding: 0 15px;
  }
  
  .quiz-header-center {
    margin: 0 15px;
  }
  
  .quiz-header-title {
    font-size: 24px;
  }
  
  .quiz-header-subtitle {
    font-size: 15px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .quiz-header {
    padding: 15px 0;
  }
  
  .quiz-header-container {
    padding: 0 15px;
  }
  
  /* Hide desktop back button on mobile */
  .quiz-header-right {
    display: none;
  }
  
  /* Show mobile menu toggle */
  .quiz-header-mobile-toggle {
    display: block;
  }
  
  /* Adjust center content for mobile */
  .quiz-header-center {
    margin: 0 15px 0 0;
    text-align: left;
    flex-direction: row;
    align-items: center;
  }
  
  .quiz-header-avatar {
    margin-bottom: 0;
    margin-right: 15px;
    flex-shrink: 0;
  }
  
  .quiz-avatar-img,
  .quiz-avatar-placeholder {
    width: 60px;
    height: 60px;
  }
  
  .quiz-header-content {
    text-align: left;
  }
  
  .quiz-header-title {
    font-size: 18px;
    margin-bottom: 8px;
  }
  
  .quiz-header-subtitle {
    font-size: 14px;
    line-height: 1.4;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .quiz-header-container {
    padding: 0 10px;
  }
  
  .quiz-header-center {
    margin: 0 10px 0 0;
  }
  
  .quiz-header-avatar {
    margin-right: 12px;
  }
  
  .quiz-avatar-img,
  .quiz-avatar-placeholder {
    width: 50px;
    height: 50px;
  }
  
  .quiz-header-title {
    font-size: 16px;
  }
  
  .quiz-header-subtitle {
    font-size: 13px;
  }
  
  .quiz-logo-img {
    max-height: 32px;
  }
}

/* Hide default header when quiz layout is used */
.quiz-page .shopify-section-header {
  display: none;
}

/* Hide quiz header content when form is shown */
.quiz-page .quiz-form-section:not([style*="display: none"]) ~ * .quiz-header-center {
  display: none;
}

/* Show simplified header when form is active */
.quiz-page .quiz-form-section:not([style*="display: none"]) ~ * .quiz-header {
  padding: 15px 0;
}

.quiz-page .quiz-form-section:not([style*="display: none"]) ~ * .quiz-header-container {
  justify-content: space-between;
}

/* Ensure quiz content is initially hidden */
.quiz-page [data-section-type="_blocks"] {
  display: none;
}
