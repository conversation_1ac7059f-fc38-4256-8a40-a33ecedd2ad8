{{ 'quiz-form.css' | asset_url | stylesheet_tag: media: 'screen' }}

<section class="quiz-form-section" id="quiz-form-section" style="background-color: {{ section.settings.background_color | default: '#f8f9fa' }};">
  <div class="quiz-form-container">
    <div class="quiz-form-content">
      <div class="quiz-form-header">
        <div class="quiz-form-avatar">
          {%- comment -%} Use same avatar as in header {%- endcomment -%}
          <div class="quiz-avatar-placeholder">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="40" cy="40" r="40" fill="#E5E5E5"/>
              <circle cx="40" cy="32" r="12" fill="#9CA3AF"/>
              <path d="M20 60c0-11.046 8.954-20 20-20s20 8.954 20 20" fill="#9CA3AF"/>
            </svg>
          </div>
        </div>
        {% if section.settings.title != blank %}
          <h2 class="quiz-form-title">{{ section.settings.title }}</h2>
        {% endif %}

        {% if section.settings.subtitle != blank %}
          <p class="quiz-form-subtitle">{{ section.settings.subtitle }}</p>
        {% endif %}
      </div>
      
      <form class="quiz-name-form" id="quiz-name-form">
        <div class="quiz-form-fields">
          <div class="quiz-form-field">
            <input 
              type="text" 
              id="quiz-first-name" 
              name="first_name" 
              placeholder="{{ section.settings.first_name_placeholder | default: 'FIRST NAME' }}"
              class="quiz-form-input"
              required
            >
          </div>
          <div class="quiz-form-field">
            <input 
              type="text" 
              id="quiz-last-name" 
              name="last_name" 
              placeholder="{{ section.settings.last_name_placeholder | default: 'LAST NAME' }}"
              class="quiz-form-input"
              required
            >
          </div>
        </div>
        
        <div class="quiz-form-button-wrapper">
          <button type="submit" class="quiz-form-button">
            {{ section.settings.button_text | default: 'NEXT' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('quiz-name-form');

  // Hide header center content when form is shown
  const headerCenter = document.querySelector('.quiz-header-center');
  if (headerCenter) {
    headerCenter.style.display = 'none';
  }

  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const firstName = document.getElementById('quiz-first-name').value.trim();
      const lastName = document.getElementById('quiz-last-name').value.trim();

      if (firstName && lastName) {
        // Store names in sessionStorage for use in quiz
        sessionStorage.setItem('quiz_first_name', firstName);
        sessionStorage.setItem('quiz_last_name', lastName);

        // Hide form and show quiz content
        const formSection = document.querySelector('.quiz-form-section');
        const quizContent = document.querySelector('[data-section-type="_blocks"]');

        if (formSection && quizContent) {
          formSection.style.display = 'none';
          quizContent.style.display = 'block';

          // Show header center content again
          if (headerCenter) {
            headerCenter.style.display = 'flex';
          }
        }

        // Trigger custom event for quiz initialization
        const event = new CustomEvent('quizNameSubmitted', {
          detail: { firstName: firstName, lastName: lastName }
        });
        document.dispatchEvent(event);
      }
    });
  }
});
</script>

{% schema %}
{
  "name": "Quiz Form",
  "class": "shopify-section-quiz-form",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Let's get started!"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Please enter your name to begin the quiz"
    },
    {
      "type": "header",
      "content": "Form Settings"
    },
    {
      "type": "text",
      "id": "first_name_placeholder",
      "label": "First Name Placeholder",
      "default": "FIRST NAME"
    },
    {
      "type": "text",
      "id": "last_name_placeholder",
      "label": "Last Name Placeholder",
      "default": "LAST NAME"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "NEXT"
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f8f9fa"
    }
  ],
  "presets": [
    {
      "name": "Quiz Form"
    }
  ]
}
{% endschema %}
