{{ 'quiz-header.css' | asset_url | stylesheet_tag: media: 'screen' }}

<header class="quiz-header" id="quiz-header">
  <div class="quiz-header-container">
    {%- comment -%} Left side - Logo {%- endcomment -%}
    <div class="quiz-header-left">
      <a href="{{ routes.root_url }}" class="quiz-header-logo">
        {%- if settings.logo -%}
          {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
          <img
            src="{{ settings.logo | image_url: width: settings.logo_width }}"
            alt="{{ settings.logo.alt | default: shop.name | escape }}"
            width="{{ settings.logo_width }}"
            height="{{ logo_height }}"
            class="quiz-logo-img"
          >
        {%- else -%}
          <span class="quiz-logo-text">{{ shop.name }}</span>
        {%- endif -%}
      </a>
    </div>

    {%- comment -%} Center - Avatar and text {%- endcomment -%}
    <div class="quiz-header-center">
      <div class="quiz-header-avatar">
        {%- if section.settings.avatar_image -%}
          <img
            src="{{ section.settings.avatar_image | image_url: width: 80 }}"
            alt="{{ section.settings.avatar_alt | default: 'Phil' | escape }}"
            width="80"
            height="80"
            class="quiz-avatar-img"
          >
        {%- else -%}
          {%- comment -%} Default avatar placeholder {%- endcomment -%}
          <div class="quiz-avatar-placeholder">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="40" cy="40" r="40" fill="#E5E5E5"/>
              <circle cx="40" cy="32" r="12" fill="#9CA3AF"/>
              <path d="M20 60c0-11.046 8.954-20 20-20s20 8.954 20 20" fill="#9CA3AF"/>
            </svg>
          </div>
        {%- endif -%}
      </div>
      <div class="quiz-header-content">
        <h1 class="quiz-header-title">
          {{ section.settings.title | default: "Hi! I'm Phil, Upstep's in-house podiatrist." }}
        </h1>
        <p class="quiz-header-subtitle">
          {{ section.settings.subtitle | default: "I'm going to ask you a few questions so I can customize your Upsteps for you. But first, I'd love to know your name." }}
        </p>
      </div>
    </div>

    {%- comment -%} Right side - Back to home button {%- endcomment -%}
    <div class="quiz-header-right">
      <a href="{{ routes.root_url }}" class="quiz-back-button">
        {{ section.settings.back_button_text | default: "Back to home" }}
      </a>
    </div>

    {%- comment -%} Mobile menu toggle {%- endcomment -%}
    <div class="quiz-header-mobile-toggle">
      <button type="button" class="quiz-mobile-menu-btn" aria-label="Toggle menu">
        <span class="quiz-hamburger-line"></span>
        <span class="quiz-hamburger-line"></span>
        <span class="quiz-hamburger-line"></span>
      </button>
    </div>
  </div>

  {%- comment -%} Mobile menu {%- endcomment -%}
  <div class="quiz-mobile-menu" id="quiz-mobile-menu">
    <div class="quiz-mobile-menu-content">
      <a href="{{ routes.root_url }}" class="quiz-mobile-back-button">
        {{ section.settings.back_button_text | default: "Back to home" }}
      </a>
    </div>
  </div>
</header>

<script>
  // Mobile menu toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.querySelector('.quiz-mobile-menu-btn');
    const mobileMenu = document.querySelector('.quiz-mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
      mobileMenuBtn.addEventListener('click', function() {
        mobileMenu.classList.toggle('active');
        mobileMenuBtn.classList.toggle('active');
      });
    }
  });
</script>

{% schema %}
{
  "name": "Quiz Header",
  "class": "shopify-section-quiz-header",
  "settings": [
    {
      "type": "header",
      "content": "Avatar Settings"
    },
    {
      "type": "image_picker",
      "id": "avatar_image",
      "label": "Avatar Image",
      "info": "Recommended size: 80x80px"
    },
    {
      "type": "text",
      "id": "avatar_alt",
      "label": "Avatar Alt Text",
      "default": "Phil"
    },
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Hi! I'm Phil, Upstep's in-house podiatrist."
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "I'm going to ask you a few questions so I can customize your Upsteps for you. But first, I'd love to know your name."
    },
    {
      "type": "header",
      "content": "Navigation Settings"
    },
    {
      "type": "text",
      "id": "back_button_text",
      "label": "Back Button Text",
      "default": "Back to home"
    }
  ],
  "presets": [
    {
      "name": "Quiz Header"
    }
  ]
}
{% endschema %}
