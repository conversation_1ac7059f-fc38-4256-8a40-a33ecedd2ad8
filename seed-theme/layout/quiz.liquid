<!doctype html>
{%- assign rtl_languages = 'ae,ar,arc,bcc,bqi,ckb,dv,fa,glk,ha,he,kwh,ks,ku,mzn,nqo,pnb,ps,sd,ug,ur,yi' | split: ',' %}
<html
  lang="{{ request.locale.iso_code }}"
  data-theme="xtra"
  dir="{% if rtl_languages contains localization.language.iso_code %}rtl{% else %}ltr{% endif %}"
  class="no-js {% if settings.enable_accessibility_default %}t1ac{% endif %}{% if request.design_mode %} theme-editor{% endif %}{% unless settings.enable_hyphens %} no-hyph{% endunless %}"
>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>
      {{ page_title }}
      {% unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless %}
    </title>
    <meta name="description" content="{{ page_description | escape }}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    <link rel="preconnect" href="https://monorail-edge.shopifysvc.com">
    <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">
    <link rel="dns-prefetch" href="https://ajax.googleapis.com">
    <link rel="dns-prefetch" href="https://maps.googleapis.com">
    <link rel="dns-prefetch" href="https://maps.gstatic.com">
    {%- if settings.favicon -%}
      <link rel="icon" href="{{ settings.favicon | image_url: width: 32, height: 32 }}" type="image/png">
      <link rel="mask-icon" href="safari-pinned-tab.svg" color="#333333">
      <link rel="apple-touch-icon" href="apple-touch-icon.png">
    {%- endif -%}
    <script>
      document.documentElement.classList.remove('no-js');
      document.documentElement.classList.add('js');
    </script>
    {% render 'social-meta-tags' %}
    {{ content_for_header }}
    {{ settings.custom_script_for_head }}
    
    {%- comment -%} Load quiz-specific styles {%- endcomment -%}
    {{ 'quiz-header.css' | asset_url | stylesheet_tag: media: 'screen' }}
    {{ 'theme.css' | asset_url | stylesheet_tag: media: 'screen' }}
    
    {%- if settings.enable_accessibility_default %}
      <link media="screen" rel="stylesheet" href="{{ 'theme-accessible.css' | asset_url }}" id="accessible-mode-css">
    {%- endif %}
  </head>
  <body class="template-{{ template.name | handle }} quiz-page">
    <div id="root">
      {%- render 'accessible-nav' -%}
      {%- comment -%} Use custom quiz header instead of header-group {%- endcomment -%}
      {%- section 'quiz-header' -%}
      <main id="content">
        {{ content_for_layout }}
        {%- sections 'footer-group' -%}
      </main>
      {%- liquid
        unless customer
          render 'mobile-login-dropdown'
        endunless
        render 'side-cart-container'
        render 'quickshop-container'
        render 'shop-the-look-container'
        render 'pickup-availability-container'

        if settings.show_newsletterpopup
          render 'newsletter-popup'
        endif
        if settings.show_age_verify_popup
          render 'age-verify-popup'
        elsif settings.show_cookiebanner != 'none'
          render 'cookie-banner'
        endif
        if settings.back_to_top_button
          render 'back-to-top-button'
        endif
        render 'upsell-popup'
      -%}
    </div>
    {% render 'global-variables' %}
    {%- if rtl_languages contains localization.language.iso_code -%}
      <link media="screen" rel="stylesheet" href="{{ 'rtl.css' | asset_url }}">
    {%- endif -%}
    {% render 'color-swatches' %}
    <script defer src="{{ 'scripts.js' | asset_url }}"></script>
    <script defer src="{{ 'custom.js' | asset_url }}"></script>
    {%- if request.design_mode -%}<script defer src="{{ 'backend-listeners.js' | asset_url }}"></script>{%- endif -%}
    {{ settings.custom_script_for_body }}
  </body>
</html>
